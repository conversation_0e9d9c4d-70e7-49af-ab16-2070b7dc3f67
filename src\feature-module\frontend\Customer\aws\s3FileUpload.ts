import {
    S3Client,
    PutObjectCommand,
    DeleteObjectCommand,
  } from "@aws-sdk/client-s3";
  
  const s3 = new S3Client({
    region: import.meta.env.VITE_S3_BUCKET_REGION,
    credentials: {
      accessKeyId: import.meta.env.VITE_S3_ACCESS_KEY,
      secretAccessKey: import.meta.env.VITE_S3_SECRET_KEY,
    },
  });
  
  const S3_BUCKET = import.meta.env.VITE_S3_BUCKET_NAME;
  const CDN_URL = import.meta.env.VITE_S3_CDN_URL;
  const S3_BASE_URL = `https://${S3_BUCKET}.s3.${
    import.meta.env.VITE_S3_BUCKET_REGION
  }.amazonaws.com/`;

  // Configuration flag to determine if CDN should be used
  const USE_CDN = CDN_URL && CDN_URL.trim() !== '' && !CDN_URL.includes('disabled');

  console.log('S3 Configuration:', {
    S3_BUCKET,
    CDN_URL: CDN_URL || 'NOT_SET',
    USE_CDN,
    S3_BASE_URL
  });
  
  export const uploadToS3 = async (file: File, folderName: string) => {
    if (!file) throw new Error("No file provided for upload");

    const key = `${folderName}/${file.name}-${Date.now()}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Body: await file.arrayBuffer(),
      ContentType: file.type,
    };

    try {
      const command = new PutObjectCommand(params);
      await s3.send(command);

      const s3Url = `${S3_BASE_URL}${key}`;
      // Use direct S3 URL for reliability instead of CDN
      const finalUrl = s3Url;

      console.log("File uploaded successfully:", {
        key,
        s3Url: finalUrl,
        cdnDisabled: !USE_CDN
      });
      return finalUrl;
    } catch (error) {
      console.error("S3 Upload Error: ", error);
      throw error;
    }
  };

  // Enhanced function to upload and return image name with metadata
  export const uploadToS3WithImageName = async (file: File, folderName: string = 'review-images') => {
    if (!file) throw new Error("No file provided for upload");

    // Create unique image name with timestamp
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const cleanFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize filename
    const imageName = `${cleanFileName}-${timestamp}.${fileExtension}`;
    const key = `${folderName}/${imageName}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Body: await file.arrayBuffer(),
      ContentType: file.type,
      // Add metadata for better file management
      Metadata: {
        'original-name': file.name,
        'upload-timestamp': timestamp.toString(),
        'file-size': file.size.toString()
      }
    };

    try {
      const command = new PutObjectCommand(params);
      await s3.send(command);

      const s3Url = `${S3_BASE_URL}${key}`;
      // Use direct S3 URL for reliability instead of CDN
      const finalUrl = s3Url;

      console.log("File uploaded successfully to S3:", {
        imageName,
        originalName: file.name,
        size: file.size,
        url: finalUrl,
        cdnDisabled: !USE_CDN
      });

      return {
        imageName,        // Just the filename for database storage
        fullUrl: finalUrl,  // Complete URL for immediate display
        key,              // S3 key for deletion
        originalName: file.name,
        size: file.size,
        timestamp
      };
    } catch (error) {
      console.error("S3 Upload Error: ", error);
      throw new Error(`Failed to upload ${file.name} to S3: ${error}`);
    }
  };

  // Helper function to construct URL from image name
  export const getImageUrlFromName = (imageName: string, folderName: string = 'review-images') => {
    if (!imageName || typeof imageName !== 'string') {
      console.warn('getImageUrlFromName: Invalid image name provided:', imageName);
      return '';
    }

    // Validate environment variables
    if (!S3_BUCKET) {
      console.error('getImageUrlFromName: S3_BUCKET is not defined. Check VITE_S3_BUCKET_NAME environment variable.');
      return '';
    }

    // Clean up image name - remove any leading/trailing whitespace
    const cleanImageName = imageName.trim();

    // Construct the key
    const key = `${folderName}/${cleanImageName}`;

    // Use direct S3 URL for reliability (CDN is currently having issues)
    const finalUrl = `${S3_BASE_URL}${key}`;

    console.log('getImageUrlFromName: Using direct S3 URL for reliability:', {
      imageName: cleanImageName,
      folderName,
      key,
      finalUrl,
      S3_BUCKET,
      S3_BASE_URL,
      cdnDisabled: !USE_CDN,
      reason: USE_CDN ? 'CDN_available_but_using_S3_for_reliability' : 'CDN_not_configured'
    });

    // Validate final URL
    if (!finalUrl.startsWith('http')) {
      console.error('getImageUrlFromName: Generated URL is invalid:', finalUrl);
      return '';
    }

    return finalUrl;
  };

  // Helper function to get image URL with fallback options
  export const getImageUrlWithFallback = (imageName: string, folderName: string = 'review-images') => {
    if (!imageName || typeof imageName !== 'string') {
      console.warn('getImageUrlWithFallback: Invalid image name provided:', imageName);
      return '';
    }

    // Clean up image name
    const cleanImageName = imageName.trim();
    const key = `${folderName}/${cleanImageName}`;

    // Always try direct S3 URL as the most reliable option
    if (S3_BUCKET && import.meta.env.VITE_S3_BUCKET_REGION) {
      const s3DirectUrl = `https://${S3_BUCKET}.s3.${import.meta.env.VITE_S3_BUCKET_REGION}.amazonaws.com/${key}`;
      console.log('getImageUrlWithFallback: Using direct S3 URL:', {
        imageName: cleanImageName,
        folderName,
        key,
        s3DirectUrl
      });
      return s3DirectUrl;
    }

    // If S3 direct URL can't be constructed, try the primary method
    const primaryUrl = getImageUrlFromName(imageName, folderName);
    console.log('getImageUrlWithFallback: Falling back to primary URL method:', primaryUrl);
    return primaryUrl;
  };

  // Enhanced function to get reliable image URL with automatic fallback
  export const getReliableImageUrl = async (imageName: string, folderName: string = 'review-images'): Promise<string> => {
    if (!imageName || typeof imageName !== 'string') {
      console.warn('getReliableImageUrl: Invalid image name provided:', imageName);
      return '';
    }

    const cleanImageName = imageName.trim();
    const key = `${folderName}/${cleanImageName}`;

    // Always use direct S3 URL for reliability
    if (S3_BUCKET && import.meta.env.VITE_S3_BUCKET_REGION) {
      const s3DirectUrl = `https://${S3_BUCKET}.s3.${import.meta.env.VITE_S3_BUCKET_REGION}.amazonaws.com/${key}`;
      console.log('getReliableImageUrl: Using direct S3 URL for reliability:', {
        imageName: cleanImageName,
        folderName,
        key,
        s3DirectUrl
      });
      return s3DirectUrl;
    }

    // Fallback to CDN if S3 direct URL can't be constructed
    if (CDN_URL && CDN_URL.trim() !== '') {
      const cleanCdnUrl = CDN_URL.endsWith('/') ? CDN_URL : `${CDN_URL}/`;
      const cdnUrl = `${cleanCdnUrl}${key}`;
      console.log('getReliableImageUrl: Falling back to CDN URL:', cdnUrl);
      return cdnUrl;
    }

    console.error('getReliableImageUrl: Unable to construct any valid URL');
    return '';
  };

  // Debug function to check S3 configuration
  export const debugS3Configuration = () => {
    console.log('=== S3 Configuration Debug ===');
    console.log('S3_BUCKET:', S3_BUCKET);
    console.log('CDN_URL:', CDN_URL);
    console.log('S3_BASE_URL:', S3_BASE_URL);
    console.log('VITE_S3_BUCKET_REGION:', import.meta.env.VITE_S3_BUCKET_REGION);
    console.log('Environment variables available:', {
      VITE_S3_BUCKET_NAME: !!import.meta.env.VITE_S3_BUCKET_NAME,
      VITE_S3_CDN_URL: !!import.meta.env.VITE_S3_CDN_URL,
      VITE_S3_BUCKET_REGION: !!import.meta.env.VITE_S3_BUCKET_REGION,
      VITE_S3_ACCESS_KEY: !!import.meta.env.VITE_S3_ACCESS_KEY,
      VITE_S3_SECRET_KEY: !!import.meta.env.VITE_S3_SECRET_KEY
    });

    // Test URL generation with a sample image
    const testImageName = 'test-image.jpg';
    const testUrl = getImageUrlFromName(testImageName, 'review-images');
    const fallbackUrl = getImageUrlWithFallback(testImageName, 'review-images');

    console.log('Test URL generation:', {
      testImageName,
      primaryUrl: testUrl,
      fallbackUrl: fallbackUrl
    });

    // Check if CDN URL is accessible
    if (CDN_URL) {
      fetch(CDN_URL, { method: 'HEAD' })
        .then(response => {
          console.log('CDN URL accessibility test:', {
            url: CDN_URL,
            status: response.status,
            accessible: response.ok
          });

          if (!response.ok) {
            console.warn('CDN is not accessible. Consider using direct S3 URLs.');
          }
        })
        .catch(error => {
          console.error('CDN URL accessibility test failed:', {
            url: CDN_URL,
            error: error.message
          });
          console.warn('CDN appears to be down. Using direct S3 URLs is recommended.');
        });
    }

    console.log('================================');
  };

  // Helper function to get multiple image URLs from names array with fallback
  export const getImageUrlsFromNames = (imageNames: string[], folderName: string = 'review-images') => {
    if (!Array.isArray(imageNames)) {
      console.warn('getImageUrlsFromNames: imageNames is not an array:', imageNames);
      return [];
    }

    const urls = imageNames
      .filter(name => name && typeof name === 'string' && name.trim() !== '')
      .map(name => {
        // Use fallback method for more reliability
        const url = getImageUrlWithFallback(name, folderName);
        if (!url) {
          console.warn('getImageUrlsFromNames: Failed to generate URL for image:', name);
        }
        return url;
      })
      .filter(url => url !== ''); // Remove empty URLs

    console.log('getImageUrlsFromNames result:', {
      inputNames: imageNames,
      folderName,
      generatedUrls: urls,
      successCount: urls.length,
      totalCount: imageNames.length,
      usingFallbackMethod: true
    });

    return urls;
  };
  
  export const deleteFromS3 = async (fileUrl: string) => {
    if (!fileUrl) throw new Error("No file URL provided for deletion");

    const fileKey = fileUrl.replace(CDN_URL, "");

    const params = {
      Bucket: S3_BUCKET,
      Key: fileKey,
    };

    try {
      const command = new DeleteObjectCommand(params);
      await s3.send(command);

      console.log("File deleted successfully:", fileUrl);
      return true;
    } catch (error) {
      console.error("S3 Delete Error: ", error);
      throw error;
    }
  };

  // Enhanced function to delete by image name
  export const deleteFromS3ByImageName = async (imageName: string, folderName: string = 'review-images') => {
    if (!imageName || typeof imageName !== 'string') {
      throw new Error("Valid image name is required for deletion");
    }

    const fileKey = `${folderName}/${imageName}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: fileKey,
    };

    try {
      const command = new DeleteObjectCommand(params);
      await s3.send(command);

      console.log("File deleted successfully from S3:", {
        imageName,
        key: fileKey
      });
      return true;
    } catch (error) {
      console.error("S3 Delete Error: ", error);
      throw new Error(`Failed to delete ${imageName} from S3: ${error}`);
    }
  };

  // Helper function to delete multiple images by names
  export const deleteMultipleFromS3ByImageNames = async (imageNames: string[], folderName: string = 'review-images') => {
    if (!Array.isArray(imageNames) || imageNames.length === 0) {
      return { success: [] as string[], failed: [] as Array<{imageName: string, error: string}> };
    }

    const results = {
      success: [] as string[],
      failed: [] as Array<{imageName: string, error: string}>
    };

    // Process deletions in parallel
    const deletePromises = imageNames.map(async (imageName) => {
      try {
        await deleteFromS3ByImageName(imageName, folderName);
        results.success.push(imageName);
      } catch (error) {
        results.failed.push({
          imageName,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    await Promise.all(deletePromises);
    return results;
  };

  // Profile picture specific functions
  export const uploadProfilePictureToS3 = async (file: File, userId: string) => {
    if (!file) throw new Error("No file provided for profile picture upload");
    if (!userId) throw new Error("User ID is required for profile picture upload");

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error("Invalid file type. Only JPEG, PNG, and WebP images are allowed.");
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error("File size too large. Maximum size is 5MB.");
    }

    // Create unique image name for profile picture
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const imageName = `profile-${userId}-${timestamp}.${fileExtension}`;
    const key = `profile-images/${imageName}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Body: await file.arrayBuffer(),
      ContentType: file.type,
      Metadata: {
        'user-id': userId,
        'original-name': file.name,
        'upload-timestamp': timestamp.toString(),
        'file-size': file.size.toString(),
        'image-type': 'profile-picture'
      }
    };

    try {
      const command = new PutObjectCommand(params);
      await s3.send(command);

      const s3Url = `${S3_BASE_URL}${key}`;
      // Use direct S3 URL for reliability instead of CDN
      const finalUrl = s3Url;

      console.log("Profile picture uploaded successfully to S3:", {
        imageName,
        userId,
        originalName: file.name,
        size: file.size,
        url: finalUrl,
        cdnDisabled: !USE_CDN
      });

      return {
        imageName,        // Just the filename for database storage
        fullUrl: finalUrl,  // Complete URL for immediate display
        key,              // S3 key for deletion
        originalName: file.name,
        size: file.size,
        timestamp,
        userId
      };
    } catch (error) {
      console.error("S3 Profile Picture Upload Error: ", error);
      throw new Error(`Failed to upload profile picture to S3: ${error}`);
    }
  };

  // Helper function to get profile picture URL from image name
  export const getProfilePictureUrl = (imageName: string) => {
    if (!imageName) return '';
    return getImageUrlFromName(imageName, 'profile-images');
  };

  // Helper function to delete old profile picture
  export const deleteProfilePictureFromS3 = async (imageName: string) => {
    if (!imageName) return true; // Nothing to delete

    try {
      await deleteFromS3ByImageName(imageName, 'profile-images');
      console.log("Old profile picture deleted successfully:", imageName);
      return true;
    } catch (error) {
      console.error("Error deleting old profile picture:", error);
      // Don't throw error for deletion failures to avoid blocking new uploads
      return false;
    }
  };